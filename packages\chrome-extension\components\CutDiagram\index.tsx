import {Button} from '@/components/ui/button'
import {Checkbox} from '@/components/ui/checkbox'
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '@/components/ui/radix-select'
import {makeExportInfo} from '@/lib/exportImgs'
import cutDiagramStore, {type CutDiagramItem} from '@/store/cutDiagramStore'
import loginStore from '@/store/loginStore'
import uploadStore from '@/store/uploadStore'
import businessStore from '@/store/uploadStore'
import {doApplyUpload, doBosUpload} from '@/utils/bos'
import {compressImg, exportBase64Image, getImgFormat} from '@/utils/common-utils'
import {toByteArray} from 'base64-js'
import {Download, ImagePlus, LogIn, Trash2, Upload} from 'lucide-react'
import {useMemo, useState} from 'react'
import {toast} from 'sonner'
import {DropdownMenuItem} from '../ui/dropdown-menu'
import {Popover, PopoverContent, PopoverTrigger} from '../ui/popover'
import Tooltip from '../ui/toolTip'
import DiagramItem from './DiagramItem'
// 主组件
export default function CutDiagram() {
  const [enableCompress, setEnableCompress] = useState(false)
  const {cutDiagrams, setCutDiagrams} = cutDiagramStore.state
  const {hasLoginInfo, figmaIdentityInfo, figmaIdentitySign, setShowLogin} = loginStore.state
  const [progressing, setProgressing] = useState(false)
  const {businessList, selectedBusiness, setSelectedBusiness, seletedBucket, setSeletedBucket} = businessStore.state
  const defaultSelectBusi = selectedBusiness || businessList[0]
  const {isLoggedIn} = loginStore.state
  const selectInfo = useMemo(() => {
    let selectAll = false
    let selectCount = 0
    let totalCount = 0
    for (let i = 0; i < cutDiagrams.length; i++) {
      if (cutDiagrams[i].isSelected) {
        selectCount++
      }
      totalCount++
    }
    if (selectCount === totalCount && selectCount > 0) {
      selectAll = true
    }
    return {
      selectAll,
      selectedCount: selectCount,
      totalCount: totalCount,
    }
  }, [cutDiagrams])

  // 处理全选
  const handleSelectAll = (checked: boolean) => {
    setCutDiagrams(
      cutDiagrams.map(item => {
        return {...item, isSelected: checked}
      }),
    )
  }

  // 处理单个项目选择
  const handleItemSelection = (itemKey: string, checked: boolean) => {
    setCutDiagrams(
      cutDiagrams.map(item => {
        return item.key === itemKey ? {...item, isSelected: checked} : item
      }),
    )
  }
  function checkWithLogin(hasLoginInfo: string) {
    if (!hasLoginInfo) {
      toast.error('请先登录哦~', {
        action: (
          <Button size="sm" onClick={() => setShowLogin(true)}>
            <LogIn className="h-4 w-4"></LogIn>快去登录
          </Button>
        ),
      })
      return false
    }
    return true
  }
  // 处理下载
  const handleDownload = async () => {
    const selectedDiagrams = await makeExportInfo(cutDiagrams.filter(item => item.isSelected))
    if (selectedDiagrams.length === 0) {
      return
    }
    try {
      setProgressing(true)
      let buffers = []
      if (enableCompress) {
        if (!checkWithLogin(hasLoginInfo)) return []
        buffers = await toast
          .promise(getBuffersWithCompress(), {
            loading: '图片压缩中...',
            success: '图片压缩完成!',
            error: (e: any) => {
              console.error('[getBuffersWithCompress->图片压缩过程中出现错误:] ', e)
              return `图片压缩失败: ${e.message}`
            },
          })
          .unwrap()
      } else {
        buffers = getBuffersWithoutCompress()
      }
      toast.promise(exportBase64Image(buffers), {
        loading: '下载文件中...',
        success: '文件已下载至本地!',
        error: (e: any) => {
          console.error('[exportBase64Image->下载过程中出现错误:] ', e)
          return `文件下载失败: ${e.message}`
        },
        finally: () => {
          setProgressing(false)
        },
      })
    } catch (error: any) {
      console.error('[handleDownload->下载过程中出现错误:] ', error)
    } finally {
      setProgressing(false)
    }

    async function getBuffersWithCompress() {
      // 如果需要压缩，异步处理每个图片
      try {
        return await Promise.all(
          selectedDiagrams.map(async item => {
            const format = getImgFormat(item.format)
            const buffer = await compressImg(toByteArray(item.content), figmaIdentityInfo, figmaIdentitySign)
            return {
              buffer,
              name: item.name,
              format,
              originFormat: item.format,
            }
          }),
        )
      } catch (e) {
        console.error('图片压缩失败:', e)
        toast.error('图片压缩失败，请查看控制台')
        return Promise.reject(e)
      }
    }

    function getBuffersWithoutCompress() {
      return selectedDiagrams.map(item => {
        const format = getImgFormat(item.format)
        return {
          buffer: toByteArray(item.content),
          name: item.name,
          format,
          originFormat: item.format,
        }
      })
    }
  }

  // 处理上传
  const handleUpload = async () => {
    if (!checkWithLogin(hasLoginInfo)) return
    if (!uploadStore.selectedBusiness) {
      toast.error('请先选择业务')
      setBusiSelectOpen(true)
      return
    }
    setBusiSelectOpen(false)
    let exportList: {content: Uint8Array; name: string; path: string; format: string}[] = []
    const selectedDiagrams = await makeExportInfo(cutDiagrams.filter(item => item.isSelected && !item.bosUrl))
    if (selectedDiagrams.length === 0) {
      return
    }
    if (enableCompress) {
      if (!checkWithLogin(hasLoginInfo)) return
      await toast
        .promise(
          Promise.all(
            selectedDiagrams.map(async item => {
              return {
                content: await compressImg(toByteArray(item.content), figmaIdentityInfo, figmaIdentitySign),
                key: item.key,
                name: item.name,
                path: item.path,
                format: item.format.toLocaleLowerCase(),
              }
            }),
          ),
          {
            loading: '图片压缩中...',
            success: '图片压缩完成!',
            error: '图片压缩失败',
          },
        )
        .unwrap()
        .then(res => {
          exportList = res
        })
    } else {
      exportList = selectedDiagrams.map(item => ({...item, content: toByteArray(item.content)}))
    }

    const fileNames = exportList.map(item => {
      return `/assets/${item.name}.${item.format}`
    })
    const businessId = uploadStore.selectedBusiness?.id
    const params = {
      businessId,
      fileNames,
      bucketId: businessId === 10000 ? 10000 : businessId === 10001 ? 10002 : businessId === 10002 ? 10003 : 0,
      configName: '切图上传Bos',
      tags: exportList.map(item => (item.format == 'svg' ? 'file' : 'image')),
      configId: 0,
      figmaIdentityInfo,
      figmaIdentitySign,
    }
    const {applyId, uploadToken} = await doApplyUpload(params)
    const fileWithBosUrls = exportList.map((item: any, idx: number) => {
      const bosUrl = `${uploadToken.baseUrl}${uploadToken.uploadFiles[idx]}`
      return {...item, bosUrl}
    })
    toast
      .promise(doBosUpload({applyId, uploadToken, files: fileWithBosUrls}), {
        loading: '切图上传中...',
        success: '切图上传成功!',
        error: '切图上传失败',
      })
      .unwrap()
      .then(() => {
        //这里会触发React的渲染
        // fileWithBosUrls.forEach(item => {
        //   const diagram = cutDiagrams.find(diagram => diagram.key === item.key)
        //   if (diagram) {
        //     newCutDiagrams.push({...diagram, bosUrl: item.bosUrl})
        //   }
        // })
        const newCutDiagrams: CutDiagramItem[] = []
        cutDiagrams.forEach(diagram => {
          const item = fileWithBosUrls.find(item => item.key === diagram.key)
          if (item) {
            newCutDiagrams.push({...diagram, bosUrl: item.bosUrl})
          } else {
            newCutDiagrams.push(diagram)
          }
        })
        setCutDiagrams(newCutDiagrams)
      })
  }

  // 处理添加切图
  const handleAddCutDiagram = async () => {
    const selection = window.figma.currentPage.selection
    if (selection.length === 0) {
      window.figma.notify('请先选择要添加的切图', {error: true})
      return Promise.reject(new Error('请先选择要添加的切图'))
    }
    const transformDiagrams = selection.map(item => ({
      id: item.id,
      name: item.name,
      format: 'png',
      scale: 1,
      width: item.width,
      height: item.height,
      isSelected: true,
      key: `${item.id}1png`,
    }))
    // 去重
    const newDiagrams = transformDiagrams.filter(item => !cutDiagrams.find(diagram => diagram.key === item.key))
    setCutDiagrams([...cutDiagrams, ...newDiagrams] as CutDiagramItem[])
  }

  // 处理删除所有切图
  const handleDeleteCutDiagram = () => {
    setCutDiagrams([])
  }

  // 开启压缩
  const handleEnableCompress = (checked: boolean) => {
    if (checked && !checkWithLogin(hasLoginInfo)) return
    setEnableCompress(checked)
  }

  useEffect(() => {
    if (isLoggedIn && businessList?.length === 0) {
      businessStore.getBuisinessList()
    }
  }, [isLoggedIn, businessList])
  const [busiSelectOpen, setBusiSelectOpen] = useState<boolean>(false)
  const handleCancel = () => {
    uploadStore.selectedBusiness = void 0
    uploadStore.seletedBucket = void 0
    setBusiSelectOpen(false)
  }
  const handleConfirm = () => {
    if (!selectedBusiness) {
      if (businessList) {
        uploadStore.selectedBusiness = businessList?.[0]
      }
    }
    if (!seletedBucket) {
      if (uploadStore.selectedBusiness) {
        uploadStore.seletedBucket = uploadStore.selectedBusiness.buckets?.[0]
      }
    }
    handleUpload()
  }
  return (
    <div className="w-full bg-white px-2">
      <div className="space-y-3 box-border">
        {/* 全选区域 */}
        <div className="box-border flex items-center space-x-3 py-2 border-b border-gray-100">
          <Checkbox checked={selectInfo.selectAll} onCheckedChange={handleSelectAll} className="mr-1" />
          <div className="flex items-center box-border">
            <span className="text-sm font-medium">全选</span>
            <span className="text-sm text-gray-500">
              （{selectInfo.selectedCount}/{selectInfo.totalCount}）
            </span>
            {/* <Tooltip className="max-w-[200px]" content={<span className="text-sm">仅提供下载，如需更换切图倍率、导出格式等，请移步Figma右下角Export功能区"</span>}>
              <HelpCircle className="h-4 w-4 text-gray-400 m-0.5" />
            </Tooltip> */}
          </div>
          <Tooltip className="max-w-[200px]" content={<span className="text-sm">添加图片</span>}>
            <Button
              variant="ghost"
              size="sm"
              className="ml-auto mr-0 border-gray-200"
              onClick={() =>
                toast.promise(handleAddCutDiagram, {
                  loading: '添加切图中...',
                  success: '切图已添加!',
                  error: (e: any) => {
                    console.error('[handleAddCutDiagram->添加切图过程中出现错误:] ', e)
                    return `切图添加失败: ${e.message}`
                  },
                })
              }
            >
              <ImagePlus className="h-4 w-4" />
            </Button>
          </Tooltip>

          <Popover>
            <PopoverTrigger asChild>
              {/* <Tooltip content={<span>删除所有切图</span>}> */}
              <Button
                variant="ghost"
                size="sm"
                className="border-gray-200 cursor-pointer mr-0"
                disabled={selectInfo.selectedCount === 0}
              >
                <Trash2 className="h-4 w-4" />
              </Button>
              {/* </Tooltip> */}
            </PopoverTrigger>
            <PopoverContent className="w-full p-2 bg-white shadow-lg relative z-99999999 border-gray-300 box-border flex flex-col gap-y-2">
              <div className="text-sm font-medium">确定要删除所有切图吗？</div>
              <Button
                size="sm"
                className="ml-auto active:scale-95 transition-transform"
                onClick={handleDeleteCutDiagram}
              >
                确定
              </Button>
            </PopoverContent>
          </Popover>
        </div>

        {/* 图片列表 */}
        <div className="space-y-1 box-border max-h-[350px] overflow-y-auto">
          {cutDiagrams?.length > 0 ? (
            cutDiagrams.map(item => (
              <DiagramItem
                key={item.key}
                item={item}
                onSelectionChange={checked => handleItemSelection(item.key, checked)}
              />
            ))
          ) : (
            <div className="text-xs text-gray-500">暂无切图</div>
          )}
        </div>

        {/* 批量操作区域 */}
        <BatchOperation
          handleFormatChange={newFormat => {
            setCutDiagrams(
              cutDiagrams.map(item => {
                return {
                  ...item,
                  format: newFormat,
                  bosUrl: '',
                  key: `${item.id}${newFormat}${item.scale}`,
                } as CutDiagramItem
              }),
            )
          }}
          handleScaleChange={newScale => {
            setCutDiagrams(
              cutDiagrams.map(item => {
                return {...item, scale: Number(newScale), bosUrl: '', key: `${item.id}${item.format}${newScale}`}
              }),
            )
          }}
        />

        {/* 底部操作区域 */}
        <div className="box-border">
          <div className="flex items-center justify-between">
            <div className="flex space-x-2">
              <Tooltip className="max-w-[200px]" content={<span className="text-sm">下载</span>}>
                <Button
                  type="button"
                  variant="ghost"
                  onClick={handleDownload}
                  disabled={selectInfo.selectedCount === 0 || progressing}
                  className="flex items-center space-x-1 text-[12px] gap-0 cursor-pointer"
                >
                  <Download className="h-4 w-4" />
                  {/* <span>下载</span> */}
                </Button>
              </Tooltip>
              {window.isInternal && (
                <Popover open={busiSelectOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      type="button"
                      variant="ghost"
                      onClick={handleUpload}
                      disabled={selectInfo.selectedCount === 0 || progressing}
                      className="flex items-center cursor-pointer space-x-1 text-[12px] gap-0"
                    >
                      <Upload className="h-4 w-4" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-full p-2 bg-white shadow-lg relative border-gray-300 box-border flex flex-col gap-y-2">
                    {businessList.length > 0 && (
                      <div className="flex items-center gap-x-2">
                        <span className="w-16 text-sm">业务选择</span>
                        <Select value={String(defaultSelectBusi.id) || ''} onValueChange={setSelectedBusiness}>
                          <SelectTrigger className="border-0 shadow-none">
                            <SelectValue placeholder="1" />
                          </SelectTrigger>
                          <SelectContent>
                            {businessList.length > 0 &&
                              businessList.map(item => (
                                <SelectItem key={item.id} value={String(item.id)}>
                                  {item.name}
                                </SelectItem>
                              ))}
                          </SelectContent>
                        </Select>
                      </div>
                    )}
                    {
                      <div className="flex items-center gap-x-2">
                        <span className="w-16 text-sm">Bucket</span>
                        <Select
                          value={String(seletedBucket?.id || defaultSelectBusi?.buckets[0]?.id)}
                          onValueChange={setSeletedBucket}
                        >
                          <SelectTrigger className="border-0 shadow-none">
                            <SelectValue placeholder="1" />
                          </SelectTrigger>
                          <SelectContent>
                            {defaultSelectBusi?.buckets.map(item => (
                              <SelectItem key={item.id + 'selected'} value={String(item.id)}>
                                {item.name}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                    }
                    <div className="flex items-center justify-end gap-x-2 mt-2">
                      <Button variant="outline" size="sm" onClick={handleCancel}>
                        取消
                      </Button>
                      <Button size="sm" onClick={handleConfirm}>
                        确认
                      </Button>
                    </div>
                  </PopoverContent>
                </Popover>
                // <Tooltip className="max-w-[200px]" content={<span className="text-sm">上传</span>}>
                //   <Button
                //     type="button"
                //     variant="ghost"
                //     onClick={handleUpload}
                //     disabled={selectInfo.selectedCount === 0 || progressing}
                //     className="flex items-center space-x-1 text-[12px] gap-0"
                //   >
                //     <Upload className="h-4 w-4" />
                //     {/* <span>上传</span> */}
                //   </Button>
                // </Tooltip>
              )}
            </div>

            {window.isInternal && (
              <div className="flex items-center ml-2">
                <span className="text-[14px] mr-1">压缩图片</span>
                {/* <Switch
                checked={compressImages}
                onCheckedChange={setCompressImages}
              /> */}
                <Checkbox checked={enableCompress} onCheckedChange={handleEnableCompress} />
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}

function BatchOperation({
  handleFormatChange,
  handleScaleChange,
}: {
  handleFormatChange: (newFormat: string) => void
  handleScaleChange: (newScale: string) => void
}) {
  const [format, setFormat] = useState<string>('PNG')
  const [scale, setScale] = useState<string>('1')

  return (
    <div className="flex items-center justify-between border-t border-gray-100 py-3 box-border m-0">
      <div className="text-sm font-medium">批量操作:</div>
      <div className="flex gap-x-2">
        <Select
          value={format}
          onValueChange={newFormat => {
            setFormat(newFormat)
            if (newFormat === 'SVG') {
              setScale('1')
            }
            handleFormatChange(newFormat)
          }}
        >
          <SelectTrigger className="!w-20 !h-6 text-xs !p-1 flex justify-center items-center">
            <SelectValue placeholder="Format" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="PNG">PNG</SelectItem>
            <SelectItem value="JPG">JPG</SelectItem>
            <SelectItem value="SVG">SVG</SelectItem>
          </SelectContent>
        </Select>
        <Select
          value={scale}
          onValueChange={newScale => {
            setScale(newScale)
            handleScaleChange(newScale)
          }}
          disabled={format === 'SVG'}
        >
          <SelectTrigger className="!w-20 !h-6 text-xs !p-1 flex justify-center items-center">
            <SelectValue placeholder="Scale" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="1">1x</SelectItem>
            <SelectItem value="2">2x</SelectItem>
            <SelectItem value="3">3x</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>
  )
}
